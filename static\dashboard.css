/* Dashboard-specific styles */
.dashboard-body {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

.dashboard-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  min-height: 100vh;
}

/* Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0.02) 100%);
  border-radius: 16px;
  margin-bottom: 24px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.1);
  box-shadow: 0 8px 32px rgba(0,0,0,0.3);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-btn {
  background: rgba(255,255,255,0.1);
  border: none;
  border-radius: 12px;
  padding: 12px;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.back-btn:hover {
  background: rgba(255,255,255,0.2);
  transform: translateX(-2px);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(16, 185, 129, 0.2);
  border-radius: 20px;
  color: #10b981;
  font-weight: 600;
  font-size: 14px;
}

.pulse-dot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.5; transform: scale(1.2); }
  100% { opacity: 1; transform: scale(1); }
}

.refresh-btn {
  background: rgba(255,255,255,0.1);
  border: none;
  border-radius: 12px;
  padding: 12px;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background: rgba(255,255,255,0.2);
  transform: rotate(180deg);
}

/* Main Dashboard Grid */
.dashboard-main {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  grid-auto-rows: min-content;
}

/* Dashboard Cards */
.dashboard-card {
  background: linear-gradient(135deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0.02) 100%);
  border-radius: 20px;
  padding: 24px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.1);
  box-shadow: 0 8px 32px rgba(0,0,0,0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.dashboard-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 48px rgba(0,0,0,0.3);
}

.dashboard-card:hover::before {
  opacity: 1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(255,255,255,0.1);
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-header h2 i {
  color: var(--primary);
}

.card-content {
  position: relative;
}

/* Summary Card Specific */
.summary-card {
  grid-column: span 2;
}

.summary-content {
  min-height: 200px;
}

.summary-content .placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-muted);
  text-align: center;
}

.summary-content .placeholder i {
  font-size: 48px;
  margin-bottom: 16px;
  color: var(--primary);
}

/* Sentiment Card */
.sentiment-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sentiment-emoji {
  font-size: 24px;
}

.sentiment-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(255,255,255,0.1);
}

.sentiment-stats .stat-item {
  text-align: center;
}

.sentiment-stats .stat-label {
  font-size: 12px;
  color: var(--text-muted);
  margin-bottom: 4px;
}

.sentiment-stats .stat-value {
  font-size: 18px;
  font-weight: 600;
}

.sentiment-stats .stat-value.positive { color: #10b981; }
.sentiment-stats .stat-value.neutral { color: #6b7280; }
.sentiment-stats .stat-value.negative { color: #ef4444; }

/* Speaking Distribution */
.speaking-distribution {
  min-height: 200px;
}

.speaking-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid rgba(255,255,255,0.05);
}

.speaking-item:last-child {
  border-bottom: none;
}

.speaker-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.speaker-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--primary), var(--secondary));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.speaker-name {
  font-weight: 500;
  color: var(--text-primary);
}

.speaking-bar {
  flex: 1;
  margin: 0 16px;
  height: 8px;
  background: rgba(255,255,255,0.1);
  border-radius: 4px;
  overflow: hidden;
}

.speaking-progress {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  border-radius: 4px;
  transition: width 0.3s ease;
}

.speaking-percentage {
  font-weight: 600;
  color: var(--text-primary);
  min-width: 40px;
  text-align: right;
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(255,255,255,0.05);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.metric-item:hover {
  background: rgba(255,255,255,0.1);
}

.metric-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: linear-gradient(45deg, var(--primary), var(--secondary));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.metric-info {
  flex: 1;
}

.metric-label {
  font-size: 12px;
  color: var(--text-muted);
  margin-bottom: 4px;
}

.metric-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

/* Leaderboard */
.leaderboard-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid rgba(255,255,255,0.05);
}

.leaderboard-item:last-child {
  border-bottom: none;
}

.rank {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--primary), var(--secondary));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 12px;
}

.participant-info {
  flex: 1;
}

.participant-name {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.participant-stats {
  font-size: 12px;
  color: var(--text-muted);
}

.engagement-score {
  font-weight: 600;
  color: var(--primary);
}

/* Recent Transcript */
.recent-transcript {
  max-height: 300px;
  overflow-y: auto;
}

.transcript-entry {
  padding: 8px 0;
  border-bottom: 1px solid rgba(255,255,255,0.05);
}

.transcript-entry:last-child {
  border-bottom: none;
}

.transcript-speaker {
  font-weight: 600;
  color: var(--primary);
  font-size: 12px;
  margin-bottom: 4px;
}

.transcript-text {
  color: var(--text-secondary);
  line-height: 1.4;
}

/* Placeholder styles */
.placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 150px;
  color: var(--text-muted);
  text-align: center;
}

.placeholder i {
  font-size: 32px;
  margin-bottom: 12px;
  color: var(--primary);
  opacity: 0.5;
}

.placeholder p {
  margin: 0;
  font-size: 14px;
}

/* Chart container */
.chart-container {
  position: relative;
  height: 200px;
  margin-bottom: 16px;
}

/* Summary Result Styles */
.summary-result {
  padding: 16px 0;
}

.summary-section {
  margin-bottom: 24px;
  padding: 16px;
  background: rgba(255,255,255,0.03);
  border-radius: 12px;
  border-left: 4px solid var(--primary);
}

.summary-section:last-child {
  margin-bottom: 0;
}

.summary-section h4 {
  margin: 0 0 12px 0;
  color: var(--text-primary);
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-section h4 i {
  color: var(--primary);
}

.summary-section p {
  margin: 0;
  color: var(--text-secondary);
  line-height: 1.6;
}

.summary-section ul {
  margin: 0;
  padding-left: 20px;
  color: var(--text-secondary);
}

.summary-section li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 150px;
  color: var(--danger);
  text-align: center;
}

.error-message i {
  font-size: 32px;
  margin-bottom: 12px;
}

.error-message p {
  margin: 0;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-main {
    grid-template-columns: 1fr;
  }

  .summary-card {
    grid-column: span 1;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-header {
    padding: 16px;
  }

  .header-left {
    gap: 12px;
  }

  .dashboard-card {
    padding: 16px;
  }
}